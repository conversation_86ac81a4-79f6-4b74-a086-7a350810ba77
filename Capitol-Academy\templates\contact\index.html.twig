{% extends 'base.html.twig' %}

{% block title %}Contact Us - Capitol Academy{% endblock %}

{% block body %}
    <!-- Section: Contact Form (Two-Column Layout with Background Image) -->
    <section class="py-5 position-relative" style="background: url('{{ asset('images/backgrounds/Background Any Question Contact Us.png') }}') center/cover; height: calc(100vh - 80px);">
        <div class="container h-100">
            <div class="row align-items-center h-100">
                <div class="col-lg-6">
                    <!-- Left Column: Title and Text -->
                    <div class="text-dark pe-lg-4">
                        <h1 class="display-3 fw-bold mb-4" style="color: #011a2d;">Any Question ? Contact us</h1>
                        <p class="lead mb-4" style="font-size: 1.3rem; line-height: 1.8; color: #343a40;">
                            Have a question or need some help ? Drop us a message below and we will be in touch as soon as possible.
                        </p>
                        <p class="mb-4" style="font-size: 1.1rem; line-height: 1.6; color: #6c757d;">
                            Our expert team is here to guide you through your financial markets education journey.
                            Whether you're a beginner or looking to advance your trading skills, we're ready to help.
                        </p>
                    </div>
                </div>
                <div class="col-lg-6">
                    <!-- Right Column: Contact Form -->
                    <div class="card border-0 shadow-lg" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 15px;">
                        <div class="card-body p-4">
                            <form action="{{ path('app_contact_unified') }}" method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="source_page" value="contact">

                                <div class="mb-3">
                                    <input type="text" name="name" class="form-control"
                                           placeholder="Name ..." required
                                           style="border: 1px solid #dee2e6; border-radius: 8px; background: #ffffff; color: #343a40; padding: 12px 15px;">
                                    <div class="invalid-feedback">Please provide your name.</div>
                                </div>

                                <div class="mb-3">
                                    <input type="email" name="email" class="form-control"
                                           placeholder="Email ..." required
                                           style="border: 1px solid #dee2e6; border-radius: 8px; background: #ffffff; color: #343a40; padding: 12px 15px;">
                                    <div class="invalid-feedback">Please provide a valid email address.</div>
                                </div>

                                <div class="mb-3">
                                    <textarea name="message" class="form-control" rows="4"
                                              placeholder="Message ..." required
                                              style="border: 1px solid #dee2e6; border-radius: 8px; background: #ffffff; color: #343a40; padding: 12px 15px; resize: vertical;"></textarea>
                                    <div class="invalid-feedback">Please provide your message.</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="privacyConsent" name="privacy_consent" checked required>
                                        <label class="form-check-label" for="privacyConsent" style="font-size: 0.85rem; line-height: 1.4; color: #6c757d;">
                                            By completing this form, I give my consent for the processing of my personal data for the purpose of providing the requested service.
                                            Personal data will be processed only for this purpose and will be protected according to our Privacy Policy and Terms and Conditions.
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn"
                                            style="background: #011a2d; border: none; padding: 12px; border-radius: 8px; font-weight: 600; color: white; transition: all 0.3s ease;"
                                            onmouseover="this.style.background='#a90418'"
                                            onmouseout="this.style.background='#011a2d'">
                                        Send Now
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<style>
/* Contact Form Specific Styles */
.form-control:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    outline: none;
}

.form-control::placeholder {
    color: #6c757d;
    opacity: 1;
}

.form-check-input:checked {
    background-color: #011a2d;
    border-color: #011a2d;
}

/* Button Hover Effects */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Form Validation Styles */
.was-validated .form-control:valid {
    border-color: #011a2d;
}

.was-validated .form-control:invalid {
    border-color: #a90418;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .display-3 {
        font-size: 2.5rem;
    }

    .lead {
        font-size: 1.1rem;
    }

    .card-body {
        padding: 2rem !important;
    }

    .py-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
    }

    /* Adjust contact section height for mobile */
    section[style*="calc(100vh - 80px)"] {
        height: auto !important;
        min-height: 100vh !important;
    }
}
</style>

<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const forms = document.querySelectorAll('.needs-validation');

    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Enhanced form field focus effects
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.style.borderColor = '#011a2d';
            this.style.boxShadow = '0 0 0 0.2rem rgba(1, 26, 45, 0.25)';
        });

        control.addEventListener('blur', function() {
            if (!this.value) {
                this.style.borderColor = '#dee2e6';
                this.style.boxShadow = 'none';
            }
        });
    });

    // Add loading state to form submission
    const contactForm = document.querySelector('form[action*="message"]');
    if (contactForm) {
        contactForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>
{% endblock %}
