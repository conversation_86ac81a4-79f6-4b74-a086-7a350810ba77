<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* home/instructors.html.twig */
class __TwigTemplate_5eb68f016af315375979c3ab8f3efd96 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'body' => [$this, 'block_body'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "home/instructors.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "home/instructors.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Instructors - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 6
        yield "<div class=\"container-fluid p-0\">
    <!-- Hero Section -->
    <section class=\"hero-section py-5\" style=\"background: linear-gradient(135deg, #f6f7f9 0%, #e9ecef 100%); min-height: 70vh;\">
        <div class=\"container\">
            <div class=\"row align-items-center justify-content-center text-center\">
                <div class=\"col-lg-10\">
                    <div class=\"hero-content\">
                        <h1 class=\"display-4 fw-bold mb-4\">
                            <span style=\"color: #011a2d;\">Meet</span> <span style=\"color: #a90418;\">Capitol Academy</span> <span style=\"color: #011a2d;\">Instructors</span>
                        </h1>
                        <p class=\"lead mb-5\" style=\"color: #495057; line-height: 1.6; font-size: 1.3rem;\">
                            We offers you the opportunity to grow for personal and professional growth as a trader. Our programs prepare beginner to seasoned traders to understand and master the different technical Analysis tools.
                        </p>

                        <!-- Instructor Cards -->
                        ";
        // line 21
        if ((($tmp = (isset($context["instructors"]) || array_key_exists("instructors", $context) ? $context["instructors"] : (function () { throw new RuntimeError('Variable "instructors" does not exist.', 21, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 22
            yield "                            <div class=\"row g-4 justify-content-center\">
                                ";
            // line 23
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["instructors"]) || array_key_exists("instructors", $context) ? $context["instructors"] : (function () { throw new RuntimeError('Variable "instructors" does not exist.', 23, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["instructor"]) {
                // line 24
                yield "                                    <div class=\"col-lg-4 col-md-6\">
                                        <div class=\"card border-0 h-100 shadow-lg instructor-card\" style=\"transition: all 0.3s ease; border-radius: 15px;\">
                                            <div class=\"card-body text-center p-4\">
                                                <!-- Instructor Image -->
                                                <div class=\"position-relative mb-4\">
                                                    <div class=\"instructor-image-container mx-auto\" style=\"width: 120px; height: 120px; border-radius: 50%; overflow: hidden; border: 4px solid #011a2d; position: relative;\">
                                                        ";
                // line 30
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "profileImage", [], "any", false, false, false, 30)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 31
                    yield "                                                            <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/instructors/" . CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "profileImage", [], "any", false, false, false, 31))), "html", null, true);
                    yield "\"
                                                                 alt=\"";
                    // line 32
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "name", [], "any", false, false, false, 32), "html", null, true);
                    yield "\"
                                                                 class=\"w-100 h-100\"
                                                                 style=\"object-fit: cover;\"
                                                                 onerror=\"this.src='";
                    // line 35
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/instructors/instructor-default-pp.png"), "html", null, true);
                    yield "'\">
                                                        ";
                } else {
                    // line 37
                    yield "                                                            <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/instructors/instructor-default-pp.png"), "html", null, true);
                    yield "\"
                                                                 alt=\"";
                    // line 38
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "name", [], "any", false, false, false, 38), "html", null, true);
                    yield "\"
                                                                 class=\"w-100 h-100\"
                                                                 style=\"object-fit: cover;\">
                                                        ";
                }
                // line 42
                yield "                                                    </div>
                                                </div>

                                                <!-- Instructor Info -->
                                                <h5 class=\"fw-bold mb-2\" style=\"color: #011a2d;\">";
                // line 46
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "name", [], "any", false, false, false, 46), "html", null, true);
                yield "</h5>
                                                ";
                // line 47
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "specialization", [], "any", false, false, false, 47)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 48
                    yield "                                                    <p class=\"text-muted mb-3\" style=\"font-size: 0.9rem;\">";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "specialization", [], "any", false, false, false, 48), "html", null, true);
                    yield "</p>
                                                ";
                }
                // line 50
                yield "
                                                ";
                // line 51
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "bio", [], "any", false, false, false, 51)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 52
                    yield "                                                    <p class=\"small text-muted mb-3\" style=\"line-height: 1.5;\">
                                                        ";
                    // line 53
                    yield (((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "bio", [], "any", false, false, false, 53)) > 100)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "bio", [], "any", false, false, false, 53), 0, 100) . "..."), "html", null, true)) : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "bio", [], "any", false, false, false, 53), "html", null, true)));
                    yield "
                                                    </p>
                                                ";
                }
                // line 56
                yield "
                                                <!-- Qualifications -->
                                                ";
                // line 58
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "qualifications", [], "any", false, false, false, 58)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 59
                    yield "                                                    <div class=\"mb-3\">
                                                        ";
                    // line 60
                    $context['_parent'] = $context;
                    $context['_seq'] = CoreExtension::ensureTraversable(Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "qualifications", [], "any", false, false, false, 60), 0, 2));
                    foreach ($context['_seq'] as $context["_key"] => $context["qualification"]) {
                        // line 61
                        yield "                                                            <span class=\"badge me-1 mb-1\" style=\"background-color: #a90418; font-size: 0.7rem;\">";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["qualification"], "html", null, true);
                        yield "</span>
                                                        ";
                    }
                    $_parent = $context['_parent'];
                    unset($context['_seq'], $context['_key'], $context['qualification'], $context['_parent']);
                    $context = array_intersect_key($context, $_parent) + $_parent;
                    // line 63
                    yield "                                                    </div>
                                                ";
                }
                // line 65
                yield "
                                                <!-- Contact Info -->
                                                <div class=\"d-flex justify-content-center gap-2\">
                                                    ";
                // line 68
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "email", [], "any", false, false, false, 68)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 69
                    yield "                                                        <a href=\"mailto:";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "email", [], "any", false, false, false, 69), "html", null, true);
                    yield "\" class=\"btn btn-sm btn-outline-primary\" style=\"border-color: #011a2d; color: #011a2d;\">
                                                            <i class=\"fas fa-envelope\"></i>
                                                        </a>
                                                    ";
                }
                // line 73
                yield "                                                    ";
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "linkedinUrl", [], "any", false, false, false, 73)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 74
                    yield "                                                        <a href=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "linkedinUrl", [], "any", false, false, false, 74), "html", null, true);
                    yield "\" target=\"_blank\" class=\"btn btn-sm btn-outline-primary\" style=\"border-color: #011a2d; color: #011a2d;\">
                                                            <i class=\"fab fa-linkedin-in\"></i>
                                                        </a>
                                                    ";
                }
                // line 78
                yield "                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['instructor'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 83
            yield "                            </div>
                        ";
        } else {
            // line 85
            yield "                            <div class=\"text-center py-5\">
                                <i class=\"fas fa-users fa-3x text-muted mb-3\"></i>
                                <h4 class=\"text-muted\">No instructors available at the moment</h4>
                                <p class=\"text-muted\">Please check back later for our expert instructors.</p>
                            </div>
                        ";
        }
        // line 91
        yield "                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 4: Contact Form (Two-Column Layout with Background Image) -->
    <section class=\"py-5 position-relative\" style=\"background: url('";
        // line 98
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/backgrounds/Background Any Question Contact Us.png"), "html", null, true);
        yield "') center/cover; height: calc(100vh - 80px);\">
        <div class=\"container h-100\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-6\">
                    <!-- Left Column: Title and Text -->
                    <div class=\"text-dark pe-lg-4\">
                        <h2 class=\"h1 fw-bold mb-4\" style=\"color: #011a2d;\">Any Question ? Contact us</h2>
                        <p class=\"lead mb-4\" style=\"font-size: 1.2rem; line-height: 1.8; color: #343a40;\">
                            Have a question or need some help ? Drop us a message below and we will be in touch as soon as possible.
                        </p>
                    </div>
                </div>
                <div class=\"col-lg-6\">
                    <!-- Right Column: Contact Form -->
                    <div class=\"card border-0 shadow-lg\" style=\"background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 15px;\">
                        <div class=\"card-body p-4\">
                            <form action=\"";
        // line 114
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_message");
        yield "\" method=\"POST\" class=\"needs-validation\" novalidate>
                                <div class=\"mb-3\">
                                    <input type=\"text\" name=\"message[name]\" class=\"form-control\"
                                           placeholder=\"Name ...\" required
                                           style=\"border: 1px solid #dee2e6; border-radius: 8px; background: #ffffff; color: #343a40; padding: 12px 15px;\">
                                    <div class=\"invalid-feedback\">Please provide your name.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <input type=\"email\" name=\"message[email]\" class=\"form-control\"
                                           placeholder=\"Email ...\" required
                                           style=\"border: 1px solid #dee2e6; border-radius: 8px; background: #ffffff; color: #343a40; padding: 12px 15px;\">
                                    <div class=\"invalid-feedback\">Please provide a valid email address.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <textarea name=\"message[message]\" class=\"form-control\" rows=\"4\"
                                              placeholder=\"Message ...\" required
                                              style=\"border: 1px solid #dee2e6; border-radius: 8px; background: #ffffff; color: #343a40; padding: 12px 15px; resize: vertical;\"></textarea>
                                    <div class=\"invalid-feedback\">Please provide your message.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <div class=\"form-check\">
                                        <input class=\"form-check-input\" type=\"checkbox\" id=\"privacyConsent\" name=\"privacy_consent\" checked required>
                                        <label class=\"form-check-label\" for=\"privacyConsent\" style=\"font-size: 0.85rem; line-height: 1.4; color: #6c757d;\">
                                            By completing this form, I give my consent for the processing of my personal data for the purpose of providing the requested service.
                                            Personal data will be processed only for this purpose and will be protected according to our Privacy Policy and Terms and Conditions.
                                        </label>
                                    </div>
                                </div>

                                <div class=\"d-grid\">
                                    <button type=\"submit\" class=\"btn\"
                                            style=\"background: #011a2d; border: none; padding: 12px; border-radius: 8px; font-weight: 600; color: white; transition: all 0.3s ease;\"
                                            onmouseover=\"this.style.background='#a90418'\"
                                            onmouseout=\"this.style.background='#011a2d'\">
                                        Send Now
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.instructor-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(1, 26, 45, 0.2) !important;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.instructor-image-container:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: #011a2d !important;
    border-color: #011a2d !important;
    color: white !important;
}

/* Contact Form Specific Styles */
.form-control:focus {
    border-color: rgba(255,255,255,0.5) !important;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25) !important;
    outline: none;
}

.form-control::placeholder {
    color: rgba(255,255,255,0.7);
    opacity: 1;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* Form Validation Styles */
.was-validated .form-control:valid {
    border-color: #28a745;
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
}

@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }

    .instructor-image-container {
        width: 100px !important;
        height: 100px !important;
    }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 224
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 225
        yield "<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const forms = document.querySelectorAll('.needs-validation');

    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Enhanced form field focus effects for contact form
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.style.borderColor = 'rgba(255,255,255,0.5)';
            this.style.boxShadow = '0 0 0 0.2rem rgba(255,255,255,0.25)';
        });

        control.addEventListener('blur', function() {
            if (!this.value) {
                this.style.borderColor = 'rgba(255,255,255,0.3)';
                this.style.boxShadow = 'none';
            }
        });
    });

    // Add loading state to form submission
    const contactForm = document.querySelector('form[action*=\"message\"]');
    if (contactForm) {
        contactForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type=\"submit\"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin me-2\"></i>Sending...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "home/instructors.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  432 => 225,  419 => 224,  299 => 114,  280 => 98,  271 => 91,  263 => 85,  259 => 83,  249 => 78,  241 => 74,  238 => 73,  230 => 69,  228 => 68,  223 => 65,  219 => 63,  210 => 61,  206 => 60,  203 => 59,  201 => 58,  197 => 56,  191 => 53,  188 => 52,  186 => 51,  183 => 50,  177 => 48,  175 => 47,  171 => 46,  165 => 42,  158 => 38,  153 => 37,  148 => 35,  142 => 32,  137 => 31,  135 => 30,  127 => 24,  123 => 23,  120 => 22,  118 => 21,  101 => 6,  88 => 5,  65 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Instructors - Capitol Academy{% endblock %}

{% block body %}
<div class=\"container-fluid p-0\">
    <!-- Hero Section -->
    <section class=\"hero-section py-5\" style=\"background: linear-gradient(135deg, #f6f7f9 0%, #e9ecef 100%); min-height: 70vh;\">
        <div class=\"container\">
            <div class=\"row align-items-center justify-content-center text-center\">
                <div class=\"col-lg-10\">
                    <div class=\"hero-content\">
                        <h1 class=\"display-4 fw-bold mb-4\">
                            <span style=\"color: #011a2d;\">Meet</span> <span style=\"color: #a90418;\">Capitol Academy</span> <span style=\"color: #011a2d;\">Instructors</span>
                        </h1>
                        <p class=\"lead mb-5\" style=\"color: #495057; line-height: 1.6; font-size: 1.3rem;\">
                            We offers you the opportunity to grow for personal and professional growth as a trader. Our programs prepare beginner to seasoned traders to understand and master the different technical Analysis tools.
                        </p>

                        <!-- Instructor Cards -->
                        {% if instructors %}
                            <div class=\"row g-4 justify-content-center\">
                                {% for instructor in instructors %}
                                    <div class=\"col-lg-4 col-md-6\">
                                        <div class=\"card border-0 h-100 shadow-lg instructor-card\" style=\"transition: all 0.3s ease; border-radius: 15px;\">
                                            <div class=\"card-body text-center p-4\">
                                                <!-- Instructor Image -->
                                                <div class=\"position-relative mb-4\">
                                                    <div class=\"instructor-image-container mx-auto\" style=\"width: 120px; height: 120px; border-radius: 50%; overflow: hidden; border: 4px solid #011a2d; position: relative;\">
                                                        {% if instructor.profileImage %}
                                                            <img src=\"{{ asset('uploads/instructors/' ~ instructor.profileImage) }}\"
                                                                 alt=\"{{ instructor.name }}\"
                                                                 class=\"w-100 h-100\"
                                                                 style=\"object-fit: cover;\"
                                                                 onerror=\"this.src='{{ asset('images/instructors/instructor-default-pp.png') }}'\">
                                                        {% else %}
                                                            <img src=\"{{ asset('images/instructors/instructor-default-pp.png') }}\"
                                                                 alt=\"{{ instructor.name }}\"
                                                                 class=\"w-100 h-100\"
                                                                 style=\"object-fit: cover;\">
                                                        {% endif %}
                                                    </div>
                                                </div>

                                                <!-- Instructor Info -->
                                                <h5 class=\"fw-bold mb-2\" style=\"color: #011a2d;\">{{ instructor.name }}</h5>
                                                {% if instructor.specialization %}
                                                    <p class=\"text-muted mb-3\" style=\"font-size: 0.9rem;\">{{ instructor.specialization }}</p>
                                                {% endif %}

                                                {% if instructor.bio %}
                                                    <p class=\"small text-muted mb-3\" style=\"line-height: 1.5;\">
                                                        {{ instructor.bio|length > 100 ? instructor.bio|slice(0, 100) ~ '...' : instructor.bio }}
                                                    </p>
                                                {% endif %}

                                                <!-- Qualifications -->
                                                {% if instructor.qualifications %}
                                                    <div class=\"mb-3\">
                                                        {% for qualification in instructor.qualifications|slice(0, 2) %}
                                                            <span class=\"badge me-1 mb-1\" style=\"background-color: #a90418; font-size: 0.7rem;\">{{ qualification }}</span>
                                                        {% endfor %}
                                                    </div>
                                                {% endif %}

                                                <!-- Contact Info -->
                                                <div class=\"d-flex justify-content-center gap-2\">
                                                    {% if instructor.email %}
                                                        <a href=\"mailto:{{ instructor.email }}\" class=\"btn btn-sm btn-outline-primary\" style=\"border-color: #011a2d; color: #011a2d;\">
                                                            <i class=\"fas fa-envelope\"></i>
                                                        </a>
                                                    {% endif %}
                                                    {% if instructor.linkedinUrl %}
                                                        <a href=\"{{ instructor.linkedinUrl }}\" target=\"_blank\" class=\"btn btn-sm btn-outline-primary\" style=\"border-color: #011a2d; color: #011a2d;\">
                                                            <i class=\"fab fa-linkedin-in\"></i>
                                                        </a>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class=\"text-center py-5\">
                                <i class=\"fas fa-users fa-3x text-muted mb-3\"></i>
                                <h4 class=\"text-muted\">No instructors available at the moment</h4>
                                <p class=\"text-muted\">Please check back later for our expert instructors.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 4: Contact Form (Two-Column Layout with Background Image) -->
    <section class=\"py-5 position-relative\" style=\"background: url('{{ asset('images/backgrounds/Background Any Question Contact Us.png') }}') center/cover; height: calc(100vh - 80px);\">
        <div class=\"container h-100\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-6\">
                    <!-- Left Column: Title and Text -->
                    <div class=\"text-dark pe-lg-4\">
                        <h2 class=\"h1 fw-bold mb-4\" style=\"color: #011a2d;\">Any Question ? Contact us</h2>
                        <p class=\"lead mb-4\" style=\"font-size: 1.2rem; line-height: 1.8; color: #343a40;\">
                            Have a question or need some help ? Drop us a message below and we will be in touch as soon as possible.
                        </p>
                    </div>
                </div>
                <div class=\"col-lg-6\">
                    <!-- Right Column: Contact Form -->
                    <div class=\"card border-0 shadow-lg\" style=\"background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 15px;\">
                        <div class=\"card-body p-4\">
                            <form action=\"{{ path('app_message') }}\" method=\"POST\" class=\"needs-validation\" novalidate>
                                <div class=\"mb-3\">
                                    <input type=\"text\" name=\"message[name]\" class=\"form-control\"
                                           placeholder=\"Name ...\" required
                                           style=\"border: 1px solid #dee2e6; border-radius: 8px; background: #ffffff; color: #343a40; padding: 12px 15px;\">
                                    <div class=\"invalid-feedback\">Please provide your name.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <input type=\"email\" name=\"message[email]\" class=\"form-control\"
                                           placeholder=\"Email ...\" required
                                           style=\"border: 1px solid #dee2e6; border-radius: 8px; background: #ffffff; color: #343a40; padding: 12px 15px;\">
                                    <div class=\"invalid-feedback\">Please provide a valid email address.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <textarea name=\"message[message]\" class=\"form-control\" rows=\"4\"
                                              placeholder=\"Message ...\" required
                                              style=\"border: 1px solid #dee2e6; border-radius: 8px; background: #ffffff; color: #343a40; padding: 12px 15px; resize: vertical;\"></textarea>
                                    <div class=\"invalid-feedback\">Please provide your message.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <div class=\"form-check\">
                                        <input class=\"form-check-input\" type=\"checkbox\" id=\"privacyConsent\" name=\"privacy_consent\" checked required>
                                        <label class=\"form-check-label\" for=\"privacyConsent\" style=\"font-size: 0.85rem; line-height: 1.4; color: #6c757d;\">
                                            By completing this form, I give my consent for the processing of my personal data for the purpose of providing the requested service.
                                            Personal data will be processed only for this purpose and will be protected according to our Privacy Policy and Terms and Conditions.
                                        </label>
                                    </div>
                                </div>

                                <div class=\"d-grid\">
                                    <button type=\"submit\" class=\"btn\"
                                            style=\"background: #011a2d; border: none; padding: 12px; border-radius: 8px; font-weight: 600; color: white; transition: all 0.3s ease;\"
                                            onmouseover=\"this.style.background='#a90418'\"
                                            onmouseout=\"this.style.background='#011a2d'\">
                                        Send Now
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.instructor-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(1, 26, 45, 0.2) !important;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.instructor-image-container:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: #011a2d !important;
    border-color: #011a2d !important;
    color: white !important;
}

/* Contact Form Specific Styles */
.form-control:focus {
    border-color: rgba(255,255,255,0.5) !important;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25) !important;
    outline: none;
}

.form-control::placeholder {
    color: rgba(255,255,255,0.7);
    opacity: 1;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* Form Validation Styles */
.was-validated .form-control:valid {
    border-color: #28a745;
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
}

@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }

    .instructor-image-container {
        width: 100px !important;
        height: 100px !important;
    }
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const forms = document.querySelectorAll('.needs-validation');

    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Enhanced form field focus effects for contact form
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.style.borderColor = 'rgba(255,255,255,0.5)';
            this.style.boxShadow = '0 0 0 0.2rem rgba(255,255,255,0.25)';
        });

        control.addEventListener('blur', function() {
            if (!this.value) {
                this.style.borderColor = 'rgba(255,255,255,0.3)';
                this.style.boxShadow = 'none';
            }
        });
    });

    // Add loading state to form submission
    const contactForm = document.querySelector('form[action*=\"message\"]');
    if (contactForm) {
        contactForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type=\"submit\"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin me-2\"></i>Sending...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>
{% endblock %}
", "home/instructors.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\home\\instructors.html.twig");
    }
}
