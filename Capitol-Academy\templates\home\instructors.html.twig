{% extends 'base.html.twig' %}

{% block title %}Instructors - Capitol Academy{% endblock %}

{% block body %}
<div class="container-fluid p-0">
    <!-- Hero Section -->
    <section class="hero-section py-5" style="background: linear-gradient(135deg, #f6f7f9 0%, #e9ecef 100%); min-height: 70vh;">
        <div class="container">
            <div class="row align-items-center justify-content-center text-center">
                <div class="col-lg-10">
                    <div class="hero-content">
                        <h1 class="display-4 fw-bold mb-4">
                            <span style="color: #011a2d;">Meet</span> <span style="color: #a90418;">Capitol Academy</span> <span style="color: #011a2d;">Instructors</span>
                        </h1>
                        <p class="lead mb-5" style="color: #495057; line-height: 1.6; font-size: 1.3rem;">
                            We offers you the opportunity to grow for personal and professional growth as a trader. Our programs prepare beginner to seasoned traders to understand and master the different technical Analysis tools.
                        </p>

                        <!-- Instructor Cards -->
                        {% if instructors %}
                            <div class="row g-4 justify-content-center">
                                {% for instructor in instructors %}
                                    <div class="col-lg-4 col-md-6">
                                        <div class="card border-0 h-100 shadow-lg instructor-card" style="transition: all 0.3s ease; border-radius: 15px;">
                                            <div class="card-body text-center p-4">
                                                <!-- Instructor Image -->
                                                <div class="position-relative mb-4">
                                                    <div class="instructor-image-container mx-auto" style="width: 120px; height: 120px; border-radius: 50%; overflow: hidden; border: 4px solid #011a2d; position: relative;">
                                                        {% if instructor.profileImage %}
                                                            <img src="{{ asset('uploads/instructors/' ~ instructor.profileImage) }}"
                                                                 alt="{{ instructor.name }}"
                                                                 class="w-100 h-100"
                                                                 style="object-fit: cover;"
                                                                 onerror="this.src='{{ asset('images/instructors/instructor-default-pp.png') }}'">
                                                        {% else %}
                                                            <img src="{{ asset('images/instructors/instructor-default-pp.png') }}"
                                                                 alt="{{ instructor.name }}"
                                                                 class="w-100 h-100"
                                                                 style="object-fit: cover;">
                                                        {% endif %}
                                                    </div>
                                                </div>

                                                <!-- Instructor Info -->
                                                <h5 class="fw-bold mb-2" style="color: #011a2d;">{{ instructor.name }}</h5>
                                                {% if instructor.specialization %}
                                                    <p class="text-muted mb-3" style="font-size: 0.9rem;">{{ instructor.specialization }}</p>
                                                {% endif %}

                                                {% if instructor.bio %}
                                                    <p class="small text-muted mb-3" style="line-height: 1.5;">
                                                        {{ instructor.bio|length > 100 ? instructor.bio|slice(0, 100) ~ '...' : instructor.bio }}
                                                    </p>
                                                {% endif %}

                                                <!-- Qualifications -->
                                                {% if instructor.qualifications %}
                                                    <div class="mb-3">
                                                        {% for qualification in instructor.qualifications|slice(0, 2) %}
                                                            <span class="badge me-1 mb-1" style="background-color: #a90418; font-size: 0.7rem;">{{ qualification }}</span>
                                                        {% endfor %}
                                                    </div>
                                                {% endif %}

                                                <!-- Contact Info -->
                                                <div class="d-flex justify-content-center gap-2">
                                                    {% if instructor.email %}
                                                        <a href="mailto:{{ instructor.email }}" class="btn btn-sm btn-outline-primary" style="border-color: #011a2d; color: #011a2d;">
                                                            <i class="fas fa-envelope"></i>
                                                        </a>
                                                    {% endif %}
                                                    {% if instructor.linkedinUrl %}
                                                        <a href="{{ instructor.linkedinUrl }}" target="_blank" class="btn btn-sm btn-outline-primary" style="border-color: #011a2d; color: #011a2d;">
                                                            <i class="fab fa-linkedin-in"></i>
                                                        </a>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">No instructors available at the moment</h4>
                                <p class="text-muted">Please check back later for our expert instructors.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 4: Contact Form (Two-Column Layout with Background Image) -->
    <section class="py-5 position-relative" style="background: url('{{ asset('images/backgrounds/Background Any Question Contact Us.png') }}') center/cover; height: calc(100vh - 80px);">
        <div class="container h-100">
            <div class="row align-items-center h-100">
                <div class="col-lg-6">
                    <!-- Left Column: Title and Text -->
                    <div class="text-dark pe-lg-4">
                        <h2 class="h1 fw-bold mb-4" style="color: #011a2d;">Any Question ? Contact us</h2>
                        <p class="lead mb-4" style="font-size: 1.2rem; line-height: 1.8; color: #343a40;">
                            Have a question or need some help ? Drop us a message below and we will be in touch as soon as possible.
                        </p>
                    </div>
                </div>
                <div class="col-lg-6">
                    <!-- Right Column: Contact Form -->
                    <div class="card border-0 shadow-lg" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 15px;">
                        <div class="card-body p-4">
                            <form action="{{ path('app_message') }}" method="POST" class="needs-validation" novalidate>
                                <div class="mb-3">
                                    <input type="text" name="message[name]" class="form-control"
                                           placeholder="Name ..." required
                                           style="border: 1px solid #dee2e6; border-radius: 8px; background: #ffffff; color: #343a40; padding: 12px 15px;">
                                    <div class="invalid-feedback">Please provide your name.</div>
                                </div>

                                <div class="mb-3">
                                    <input type="email" name="message[email]" class="form-control"
                                           placeholder="Email ..." required
                                           style="border: 1px solid #dee2e6; border-radius: 8px; background: #ffffff; color: #343a40; padding: 12px 15px;">
                                    <div class="invalid-feedback">Please provide a valid email address.</div>
                                </div>

                                <div class="mb-3">
                                    <textarea name="message[message]" class="form-control" rows="4"
                                              placeholder="Message ..." required
                                              style="border: 1px solid #dee2e6; border-radius: 8px; background: #ffffff; color: #343a40; padding: 12px 15px; resize: vertical;"></textarea>
                                    <div class="invalid-feedback">Please provide your message.</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="privacyConsent" name="privacy_consent" checked required>
                                        <label class="form-check-label" for="privacyConsent" style="font-size: 0.85rem; line-height: 1.4; color: #6c757d;">
                                            By completing this form, I give my consent for the processing of my personal data for the purpose of providing the requested service.
                                            Personal data will be processed only for this purpose and will be protected according to our Privacy Policy and Terms and Conditions.
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn"
                                            style="background: #011a2d; border: none; padding: 12px; border-radius: 8px; font-weight: 600; color: white; transition: all 0.3s ease;"
                                            onmouseover="this.style.background='#a90418'"
                                            onmouseout="this.style.background='#011a2d'">
                                        Send Now
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.instructor-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(1, 26, 45, 0.2) !important;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.instructor-image-container:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: #011a2d !important;
    border-color: #011a2d !important;
    color: white !important;
}

/* Contact Form Specific Styles */
.form-control:focus {
    border-color: rgba(255,255,255,0.5) !important;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25) !important;
    outline: none;
}

.form-control::placeholder {
    color: rgba(255,255,255,0.7);
    opacity: 1;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* Form Validation Styles */
.was-validated .form-control:valid {
    border-color: #28a745;
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
}

@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }

    .instructor-image-container {
        width: 100px !important;
        height: 100px !important;
    }

    /* Adjust contact section height for mobile */
    section[style*="calc(100vh - 80px)"] {
        height: auto !important;
        min-height: 100vh !important;
    }
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const forms = document.querySelectorAll('.needs-validation');

    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Enhanced form field focus effects for contact form
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.style.borderColor = 'rgba(255,255,255,0.5)';
            this.style.boxShadow = '0 0 0 0.2rem rgba(255,255,255,0.25)';
        });

        control.addEventListener('blur', function() {
            if (!this.value) {
                this.style.borderColor = 'rgba(255,255,255,0.3)';
                this.style.boxShadow = 'none';
            }
        });
    });

    // Add loading state to form submission
    const contactForm = document.querySelector('form[action*="message"]');
    if (contactForm) {
        contactForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>
{% endblock %}
